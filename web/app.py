"""
Web interface for the NewsMonitor project.

This module provides a Flask web interface for displaying SP500 index graphs,
financial news, and predictions.
"""

from web.data.prediction_service import get_prediction, get_llm_prediction
from web.data.news_data import get_all_news
from web.data.sp500_data import get_sp500_data
from web.utils.error_handlers import handle_error
from utils.logging_config import get_web_logger
from web.config import (
    HOST,
    PORT,
    DEBUG,
    CRAWLER_RUN_INTERVAL_HOURS,
    CRAWLER_DISABLED_BY_DEFAULT
)
import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from flask import Flask, render_template, jsonify, request
from apscheduler.schedulers.background import BackgroundScheduler

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import configuration

# Import utilities

# Import data modules

# Configure logging
logger = get_web_logger(__name__)

# Create Flask app
app = Flask(__name__)


@app.route('/')
def index():
    """Render the main page with the S&P 500 graph"""
    return render_template('index.html')


@app.route('/api/sp500')
def sp500_data():
    """API endpoint to get S&P 500 data"""
    start_date = request.args.get('start_date', '2025-01-01')
    end_date = request.args.get(
        'end_date', datetime.now().strftime('%Y-%m-%d'))

    try:
        data = get_sp500_data(start_date, end_date)
        return jsonify(data)
    except Exception as e:
        return handle_error(e, logger)


@app.route('/api/news')
def get_news():
    """
    Get news articles with optional filtering.
    Supports filtering by date range, sentiment, market indicator, and search keyword.
    """
    try:
        # Get query parameters
        limit = request.args.get('limit', default=20, type=int)
        start_date = request.args.get('start_date', default=None, type=str)
        end_date = request.args.get('end_date', default=None, type=str)
        sentiment = request.args.get('sentiment', default=None, type=str)
        indicator = request.args.get('indicator', default=None, type=str)
        search_keyword = request.args.get('search', default=None, type=str)

        # Log the request parameters
        logger.info(
            f"API request for news with limit: {limit}, date range: {start_date} to {end_date}, sentiment: {sentiment}, indicator: {indicator}, search: {search_keyword}")

        # Get news articles
        all_news_data = get_all_news(
            limit=limit,
            start_date=start_date,
            end_date=end_date,
            sentiment=sentiment,
            indicator=indicator,
            search_keyword=search_keyword
        )

        return jsonify(all_news_data)

    except Exception as e:
        logger.error(f"Error in get_news: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/prediction')
def prediction():
    """API endpoint to get the prediction for the next trading day's SPY price"""
    try:
        prediction_data = get_prediction()
        return jsonify(prediction_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/llm-prediction')
def llm_prediction():
    """API endpoint to get LLM-based market prediction"""
    try:
        # Get optional parameters
        preferred_api = request.args.get('api', 'openai')

        # Get LLM prediction (run async function in sync context)
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        prediction_data = loop.run_until_complete(
            get_llm_prediction(preferred_api=preferred_api))
        return jsonify(prediction_data)
    except Exception as e:
        logger.error(f"Error in LLM prediction endpoint: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/prediction-history')
def prediction_history():
    """API endpoint to get historical prediction data"""
    from web.data.prediction_service import get_prediction_history

    # Get limit parameter (default to 10)
    limit = request.args.get('limit', default=10, type=int)

    try:
        history_data = get_prediction_history(limit=limit)
        return jsonify(history_data)
    except Exception as e:
        logger.error(f"Error getting prediction history: {e}")
        return jsonify({'error': str(e)}), 500


def is_port_in_use(host, port):
    """Check if a port is already in use."""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            s.bind((host, port))
            return False
    except socket.error:
        return True


def find_free_port(start_port, max_attempts=10):
    """Find a free port starting from start_port."""
    port = start_port
    for _ in range(max_attempts):
        if not is_port_in_use('0.0.0.0', port):
            return port
        port += 1
    return None


def main():
    """Entry point for the package."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Start the web server')
    parser.add_argument('--port', type=int, default=PORT,
                        help=f'Port to run the server on (default: {PORT})')
    parser.add_argument('--host', type=str, default=HOST,
                        help=f'Host to run the server on (default: {HOST})')
    parser.add_argument('--debug', action='store_true',
                        default=DEBUG, help='Run in debug mode')
    parser.add_argument('--no-reloader', action='store_true',
                        help='Disable the auto-reloader in debug mode')
    parser.add_argument('--force-port', action='store_true',
                        help='Force using the specified port even if it requires finding an alternative')
    args = parser.parse_args()

    # Check if the port is already in use
    if is_port_in_use(args.host, args.port):
        if args.force_port:
            # Try to find an alternative port
            free_port = find_free_port(args.port + 1)
            if free_port:
                logger.info(f"Using alternative port {free_port} instead")
                args.port = free_port
            else:
                logger.error(
                    "Could not find a free port. Please stop the existing server or specify a different port.")
                return 1
        else:
            logger.error(
                f"Port {args.port} is already in use. Please use --force-port to find an alternative port or specify a different port with --port.")
            return 1

    # Start the web server
    logger.info(f"Starting web server on {args.host}:{args.port}")
    try:
        # Configure Flask run options
        run_options = {
            'debug': args.debug,
            'host': args.host,
            'port': args.port
        }

        # If in debug mode, handle reloader settings
        if args.debug:
            if args.no_reloader:
                logger.info("Starting in debug mode with reloader disabled")
                run_options['use_reloader'] = False
            else:
                logger.info("Starting in debug mode with reloader enabled")
                run_options['threaded'] = False

        # Start the Flask app with the configured options
        app.run(**run_options)
        return 0
    except OSError as e:
        logger.error(f"Error starting server: {e}")
        return 1


if __name__ == '__main__':
    main()
